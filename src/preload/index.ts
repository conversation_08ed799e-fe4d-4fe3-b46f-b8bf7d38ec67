import type {
  LogChannel,
  LoggerConfig,
  LogLevel,
  MetadataCompleteData,
  MetadataProgressData,
  ProcessStatusUpdate,
  TextToSpeechParams,
  TTSProcess,
  Voice
} from "@types";

const {contextBridge, ipcRenderer} = require('electron');

// Import logger preload
require('./logger');

// Định nghĩa kiểu dữ liệu cho IpcRendererEvent
type IpcRendererEvent = any;

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Store operations
  getStoreValue: (key: string) => ipcRenderer.invoke('getStoreValue', key),
  setStoreValue: (key: string, value: any) => ipcRenderer.invoke('setStoreValue', key, value),

  // Dialog operations
  selectFolder: () => ipcRenderer.invoke('selectFolder'),
  selectVoiceZip: () => ipcRenderer.invoke('selectVoiceZip'),
  selectExportPath: (defaultPath?: string) => ipcRenderer.invoke('selectExportPath', defaultPath),

  // File operations
  getAudioFiles: (folderPath: string) => ipcRenderer.invoke('getAudioFiles', folderPath),

  // Voice operations
  getVoices: () => ipcRenderer.invoke('getVoices'),
  getVoice: (id: string) => ipcRenderer.invoke('getVoice', id),
  addVoice: (voice: Voice) => ipcRenderer.invoke('addVoice', voice),
  updateVoice: (voice: Voice) => ipcRenderer.invoke('updateVoice', voice),
  deleteVoice: (id: string) => ipcRenderer.invoke('deleteVoice', id),
  setDefaultVoice: (voiceId: string) => ipcRenderer.invoke('setDefaultVoice', voiceId),
  getDefaultVoice: () => ipcRenderer.invoke('getDefaultVoice'),
  importVoice: (zipPath: string) => ipcRenderer.invoke('importVoice', zipPath),
  exportVoice: (voiceId: string, outputDir: string) => ipcRenderer.invoke('exportVoice', voiceId, outputDir),

  // TTS operations
  textToSpeech: (options: TextToSpeechParams) => ipcRenderer.invoke('textToSpeech', options),
  getTTSProcesses: () => ipcRenderer.invoke('getTTSProcesses'),
  createProcess: (process: TTSProcess) => ipcRenderer.invoke('createProcess', process),
  updateProcess: (process: TTSProcess) => ipcRenderer.invoke('updateProcess', process),
  removeProcess: (processId: string) => ipcRenderer.invoke('removeProcess', processId),
  cancelProcess: (processId: string) => ipcRenderer.invoke('cancelProcess', processId),
  cleanupCompletedProcesses: () => ipcRenderer.invoke('cleanupCompletedProcesses'),

  // Metadata operations
  processMetadata: (params: { processId: string, inputFolder: string, voiceName: string, language?: string }) =>
      ipcRenderer.invoke('processMetadata', params),

  // Finetune operations
  startFinetune: (params: { voiceName: string, trainMetadataPath?: string, evalMetadataPath?: string, epochs?: number, testText?: string }) =>
      ipcRenderer.invoke('startFinetune', params),
  cancelFinetune: (processId: string) => ipcRenderer.invoke('cancelFinetune', processId),
  getFinetuneStatus: (processId: string) => ipcRenderer.invoke('getFinetuneStatus', processId),

  // Open userData folder
  openUserDataFolder: () => ipcRenderer.invoke('openUserDataFolder'),

  // Logger operations
  loggerLog: (level: LogLevel, message: string, data?: any, channel?: LogChannel) =>
      ipcRenderer.invoke('loggerLog', level, message, data, channel),
  getLogs: (filter?: any) => ipcRenderer.invoke('getLogs', filter),
  clearLogs: () => ipcRenderer.invoke('clearLogs'),
  getLogConfig: () => ipcRenderer.invoke('getLogConfig'),
  updateLogConfig: (config: Partial<LoggerConfig>) => ipcRenderer.invoke('updateLogConfig', config),

  // Toast operations
  showToast: (type: 'error' | 'warning' | 'success' | 'info', message: string) =>
      ipcRenderer.invoke('showToast', {type, message}),

  // Event listeners
  onProcessStatusUpdate: (callback: (data: ProcessStatusUpdate) => void) => {
    ipcRenderer.on('processStatusUpdate', (_: IpcRendererEvent, data: ProcessStatusUpdate) => callback(data));
    return () => ipcRenderer.removeAllListeners('processStatusUpdate');
  },
  onToast: (callback: (data: { type: 'error' | 'warning' | 'success', message: string }) => void) => {
    ipcRenderer.on('toast', (_: IpcRendererEvent, data: any) => callback(data));
    return () => ipcRenderer.removeAllListeners('toast');
  },
  onFinetuneProgress: (callback: (data: { processId: string, progress: number }) => void) => {
    ipcRenderer.on('finetuneProgress', (_: IpcRendererEvent, data: any) => callback(data));
    return () => ipcRenderer.removeAllListeners('finetuneProgress');
  },
  onFinetuneLog: (callback: (data: { processId: string, log: string }) => void) => {
    ipcRenderer.on('finetuneLog', (_: IpcRendererEvent, data: any) => callback(data));
    return () => ipcRenderer.removeAllListeners('finetuneLog');
  },
  onFinetuneComplete: (callback: (data: { processId: string, result: any }) => void) => {
    ipcRenderer.on('finetuneComplete', (_: IpcRendererEvent, data: any) => callback(data));
    return () => ipcRenderer.removeAllListeners('finetuneComplete');
  },
  onFinetuneError: (callback: (data: { processId: string, error: string }) => void) => {
    ipcRenderer.on('finetuneError', (_: IpcRendererEvent, data: any) => callback(data));
    return () => ipcRenderer.removeAllListeners('finetuneError');
  },
  onMetadataProgress: (callback: (data: MetadataProgressData) => void) => {
    ipcRenderer.on('metadataProgress', (_: IpcRendererEvent, data: any) => callback(data));
    return () => ipcRenderer.removeAllListeners('metadataProgress');
  },
  onMetadataComplete: (callback: (data: MetadataCompleteData) => void) => {
    ipcRenderer.on('metadataComplete', (_: IpcRendererEvent, data: any) => callback(data));
    return () => ipcRenderer.removeAllListeners('metadataComplete');
  },
  selectFiles: (options: {
    filters: { name: string; extensions: string[] }[];
    properties: ('openFile' | 'openDirectory' | 'multiSelections')[];
    maxFiles?: number;
  }) => ipcRenderer.invoke('selectFiles', options),
  reloadChunk: (params: {
    processId: string;
    chunkId: string;
    text: string;
    voice: Voice;
  }) => ipcRenderer.invoke('reloadChunk', params),
  getAudioDuration: (audioPath: string) => ipcRenderer.invoke('getAudioDuration', audioPath),
  selectOutputFolder: () => ipcRenderer.invoke('select-output-folder'),
  selectReferenceFiles: () => ipcRenderer.invoke('select-reference-files'),
  previewVoice: (voice: Voice) => ipcRenderer.invoke('preview-voice', voice)
});
