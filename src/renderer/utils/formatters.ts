/**
 * <PERSON><PERSON><PERSON> hàm định dạng dữ liệu
 */

/**
 * Định dạng ngày tháng
 * @param dateString Chuỗi ngày tháng
 * @param includeTime Có hiển thị giờ phút giây không
 * @returns Chuỗi ngày tháng đã định dạng
 */
export const formatDate = (dateString: string, includeTime = false): string => {
  try {
    const date = new Date(dateString);

    // Kiểm tra xem date có hợp lệ không
    if (isNaN(date.getTime())) {
      return dateString;
    }

    // Định dạng ngày tháng
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    };

    // Thêm giờ phút giây nếu cần
    if (includeTime) {
      options.hour = '2-digit';
      options.minute = '2-digit';
      options.second = '2-digit';
    }

    return date.toLocaleDateString('vi-VN', options);
  } catch (error) {
    console.error('Lỗi khi định dạng ngày tháng:', error);
    return dateString;
  }
};

/**
 * Định dạng thời lượng audio
 * @param seconds Thời lượng tính bằng giây
 * @returns Chuỗi thời lượng đã định dạng (mm:SS hoặc HH:mm:SS)
 */
export const formatDuration = (seconds: number): string => {
  try {
    if (isNaN(seconds) || seconds < 0) {
      return '00:00';
    }

    // Làm tròn đến số nguyên
    const totalSeconds = Math.round(seconds);

    // Tính toán giờ, phút và giây
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const remainingSeconds = totalSeconds % 60;

    if (hours > 0) {
      // Định dạng HH:mm:SS khi vượt quá 1 tiếng
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      // Định dạng mm:SS khi dưới 1 tiếng
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  } catch (error) {
    console.error('Lỗi khi định dạng thời lượng:', error);
    return '00:00';
  }
};

/**
 * Định dạng kích thước file
 * @param bytes Kích thước tính bằng byte
 * @returns Chuỗi kích thước đã định dạng
 */
export const formatFileSize = (bytes: number): string => {
  try {
    if (isNaN(bytes) || bytes < 0) {
      return '0 B';
    }

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    // Làm tròn đến 2 chữ số thập phân
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  } catch (error) {
    console.error('Lỗi khi định dạng kích thước file:', error);
    return '0 B';
  }
};

/**
 * Rút gọn văn bản nếu quá dài
 * @param text Văn bản cần rút gọn
 * @param maxLength Độ dài tối đa
 * @returns Văn bản đã rút gọn
 */
export const truncateText = (text: string, maxLength = 50): string => {
  try {
    if (!text) return '';

    if (text.length <= maxLength) {
      return text;
    }

    return `${text.substring(0, maxLength)}...`;
  } catch (error) {
    console.error('Lỗi khi rút gọn văn bản:', error);
    return text;
  }
};
