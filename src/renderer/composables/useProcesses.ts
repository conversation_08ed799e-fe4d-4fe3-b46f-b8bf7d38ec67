import { ref, computed } from 'vue';
import type { TTSProcess, ProcessStatusUpdate } from '@types';
import { useToast } from './useToast';
import { useI18n } from 'vue-i18n';

export function useProcesses() {
  const { t } = useI18n();
  const { showToast } = useToast();

  const processes = ref<TTSProcess[]>([]);
  const expandedProcesses = ref<Set<string>>(new Set());

  const sortedProcesses = computed(() => {
    console.log('Recalculating sortedProcesses', processes.value.length);
    return [...processes.value].sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  });

  // Load processes
  const loadProcesses = async () => {
    try {
      if (!window.electronAPI) {
        console.error('electronAPI không khả dụng');
        return;
      }
      const loadedProcesses = await window.electronAPI.getTTSProcesses();
      // console.log("loadedProcesses chunks: ", loadedProcesses.map(p => ({ id: p.id, chunksLength: p.chunks?.length || 0 })));
      if (loadedProcesses && Array.isArray(loadedProcesses)) {
        processes.value = loadedProcesses;
      } else {
        console.error('Invalid processes data:', loadedProcesses);
        showToast('error', t('home.process.errors.loadFailed'));
      }
    } catch (error) {
      console.error('Lỗi khi load danh sách processes:', error);
      showToast('error', t('home.process.errors.loadFailed'));
    }
  };

  // Create process
  const createProcess = async (newProcess: TTSProcess) => {
    try {
      // 1. Add the new process to the frontend list immediately
      // Assign a temporary ID or ensure newProcess already has a unique ID
      // (Assuming newProcess comes with a unique ID from HomeView)
      processes.value.unshift(newProcess); // Add to the beginning of the list
      await window.electronAPI.createProcess(JSON.parse(JSON.stringify(newProcess)));
      console.log('Process added to frontend list immediately:', newProcess.id, processes.value.length);

      // 2. Create process in backend store and initiate TTS processing
      // We send the newProcess object to the backend. It should use the provided ID.
      const ttsResult = await window.electronAPI.textToSpeech({
        processId: newProcess.id, // Use the ID from the newProcess object
        text: newProcess.text,
        voice: JSON.parse(JSON.stringify(newProcess.voice)),
        outputPath: newProcess.outputPath,
        fileName: newProcess.fileName,
        language: 'en'
        // ... additional parameters from newProcess if needed ...
      });

      if (ttsResult && 'error' in ttsResult) {
        // Handle error if textToSpeech fails to start
        console.error('Error initiating textToSpeech:', ttsResult.error);
        showToast('error', ttsResult.error);
        // Optional: Update the status of this specific process to 'failed' in the frontend list
        // Find the process by ID and update its status/error message
         const index = processes.value.findIndex(p => p.id === newProcess.id);
         if (index !== -1) {
           processes.value[index].status = 'failed';
           processes.value[index].error = ttsResult.error;
           processes.value[index].updatedAt = new Date().toISOString();
         }
      } else {
        // Process initiated successfully. Backend should send status updates.
        console.log('TextToSpeech initiated for process:', newProcess.id);
      }

      return newProcess;
    } catch (error) {
      console.error('Error creating or initiating process:', error);
      showToast('error', String(error));

      // Also update the process status to failed in case of a sync error before backend call completes
      const index = processes.value.findIndex(p => p.id === newProcess.id);
      if (index !== -1) {
        processes.value[index].status = 'failed';
        processes.value[index].error = String(error);
        processes.value[index].updatedAt = new Date().toISOString();
      }

      throw error; // Re-throw the error to be handled by the caller (HomeView)
    }
  };

  // Update process status
  const updateProcessStatus = (data: ProcessStatusUpdate) => {
    const processIndex = processes.value.findIndex((p) => p.id === data.processId);
    if (processIndex !== -1) {
      const currentProcess = processes.value[processIndex];

      // Nếu status chuyển sang processing hoặc running và chưa có startTime, thì gán startTime
      const startTime = (data.status === 'processing' || data.status === 'running') && !currentProcess.startTime
        ? new Date().toISOString()
        : currentProcess.startTime;

      // Cập nhật chunk info nếu có
      if (data.chunkInfo) {
        // Nếu chưa có chunks, reload processes để lấy chunks mới
        if (!currentProcess.chunks || currentProcess.chunks.length === 0) {
          console.log('No chunks found, reloading processes to get chunks data');
          loadProcesses();
          return;
        }

        const chunkIndex = currentProcess.chunks.findIndex(c => c.id === data.chunkInfo!.id);
        if (chunkIndex !== -1) {
          currentProcess.chunks[chunkIndex] = {
            ...currentProcess.chunks[chunkIndex],
            ...data.chunkInfo
          };
        }
      }

      // Xử lý thông báo tất cả chunk đã hoàn thành
      if (data.allChunksCompleted && currentProcess.chunks) {
        console.log('Tất cả chunk đã hoàn thành, chuẩn bị merge audio', {
          processId: data.processId,
          totalChunks: currentProcess.chunks.length,
          completedChunks: currentProcess.chunks.filter(c => c.status === 'completed').length
        });
      }

      const updatedProcess = {
        ...currentProcess,
        status: data.status,
        progress: data.progress ?? currentProcess.progress,
        error: data.error,
        audioPath: data.audioPath,
        executionTime: data.executionTime ?? currentProcess.executionTime,
        duration: data.duration ?? currentProcess.duration,
        startTime,
        updatedAt: data.updatedAt || new Date().toISOString(),
        // Đảm bảo chunks được preserve
        chunks: currentProcess.chunks || [],
      };

      processes.value[processIndex] = updatedProcess;
    } else {
      console.warn('Process not found in current list, reloading processes...', data.processId);
      loadProcesses();
    }
  };

  // Cancel process
  const cancelProcess = async (processId: string) => {
    try {
      await window.electronAPI.cancelProcess(processId);
      showToast('success', t('home.process.actions.cancelSuccess'));
    } catch (error) {
      console.error('Error cancelling process:', error);
      showToast('error', String(error));
      throw error;
    }
  };

  // Delete process
  const deleteProcess = async (processId: string) => {
    try {
      await window.electronAPI.removeProcess(processId);
      processes.value = processes.value.filter(p => p.id !== processId);
      showToast('success', t('home.process.actions.deleteSuccess'));
    } catch (error) {
      console.error('Error deleting process:', error);
      showToast('error', String(error));
      throw error;
    }
  };

  // Cleanup processes
  const cleanupProcesses = async () => {
    try {
      const allProcesses = await window.electronAPI.getTTSProcesses();
      const processesToRemove = allProcesses.filter(p =>
        p.status === 'completed' || p.status === 'failed' || p.status === 'cancelled'
      );

      for (const process of processesToRemove) {
        await window.electronAPI.removeProcess(process.id);
      }

      processes.value = processes.value.filter(p =>
        p.status !== 'completed' && p.status !== 'failed' && p.status !== 'cancelled'
      );

      showToast('success', t('home.process.actions.cleanupSuccess'));
    } catch (error) {
      console.error('Error cleaning up processes:', error);
      showToast('error', t('home.process.actions.cleanupError'));
      throw error;
    }
  };

  // Toggle process details
  const toggleProcessDetails = (processId: string) => {
    if (expandedProcesses.value.has(processId)) {
      expandedProcesses.value.delete(processId);
    } else {
      expandedProcesses.value.add(processId);
    }
  };

  return {
    processes,
    expandedProcesses,
    sortedProcesses,
    loadProcesses,
    createProcess,
    updateProcessStatus,
    cancelProcess,
    deleteProcess,
    cleanupProcesses,
    toggleProcessDetails
  };
}