import {computed, ref} from 'vue';
import type {ProcessStatusUpdate, TTSProcess} from '@types';
import {useToast} from './useToast';
import {useI18n} from 'vue-i18n';

export function useProcesses() {
  const { t } = useI18n();
  const { showToast } = useToast();

  const processes = ref<TTSProcess[]>([]);
  const expandedProcesses = ref<Set<string>>(new Set());

  /**
   * Tạo filename duy nhất bằng cách thêm hậu tố số nếu cần
   * @returns Tên file duy nhất
   * @param ttsProcess
   */
  const generateUniqueFileName = async (ttsProcess: TTSProcess): Promise<string> => {
    const extension = '.wav';
    let fileName = ttsProcess.fileName;
    let counter = 1;

    // Kiểm tra file đầu tiên
    let fullPath = `${ttsProcess.outputPath}/${fileName}${extension}`;

    try {
      while (await isExistFile(fullPath)) {
        fileName = `${ttsProcess.fileName} (${counter})`;
        fullPath = `${ttsProcess.outputPath}/${fileName}${extension}`;
        counter++;

        if (counter > 10) {
          fileName = `${ttsProcess.fileName} (${ttsProcess.id.substring(0, 8)})`;
          break;
        }
      }
    } catch (error) {
      console.warn('Lỗi khi kiểm tra file tồn tại, sử dụng tên gốc:', error);
    }

    return fileName;
  };

  /**
   * Kiểm tra file có tồn tại không thông qua electronAPI
   * @param filePath Đường dẫn file
   * @returns Promise<boolean>
   */
  const isExistFile = async (filePath: string): Promise<boolean> => {
    try {
      if (!window.electronAPI?.isExistFile) {
        console.warn('electronAPI.isExistFile không khả dụng');
        return false;
      }
      return await window.electronAPI.isExistFile(filePath);
    } catch (error) {
      console.warn('Lỗi khi kiểm tra file tồn tại:', error);
      return false;
    }
  };

  const sortedProcesses = computed(() => {
    console.log('Recalculating sortedProcesses', processes.value.length);
    return [...processes.value].sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  });

  // Load processes
  const loadProcesses = async () => {
    try {
      if (!window.electronAPI) {
        console.error('electronAPI không khả dụng');
        return;
      }
      const loadedProcesses = await window.electronAPI.getTTSProcesses();
      // console.log("loadedProcesses chunks: ", loadedProcesses.map(p => ({ id: p.id, chunksLength: p.chunks?.length || 0 })));
      if (loadedProcesses && Array.isArray(loadedProcesses)) {
        processes.value = loadedProcesses;
      } else {
        console.error('Invalid processes data:', loadedProcesses);
        showToast('error', t('home.process.errors.loadFailed'));
      }
    } catch (error) {
      console.error('Lỗi khi load danh sách processes:', error);
      showToast('error', t('home.process.errors.loadFailed'));
    }
  };

  // Create process
  const createProcess = async (newProcess: TTSProcess) => {
    try {
      // 1. Tạo filename duy nhất trước khi tạo process
      const uniqueFileName = await generateUniqueFileName(newProcess);

      // Cập nhật fileName trong process
      const processWithUniqueFileName = {
        ...newProcess,
        fileName: uniqueFileName
      };

      // 2. Add the new process to the frontend list immediately
      // Assign a temporary ID or ensure newProcess already has a unique ID
      // (Assuming newProcess comes with a unique ID from HomeView)
      processes.value.unshift(processWithUniqueFileName); // Add to the beginning of the list
      await window.electronAPI.createProcess(JSON.parse(JSON.stringify(processWithUniqueFileName)));
      console.log('Process added to frontend list immediately:', processWithUniqueFileName.id, processes.value.length);

      // 3. Create process in backend store and initiate TTS processing
      // We send the processWithUniqueFileName object to the backend. It should use the provided ID.
      const ttsResult = await window.electronAPI.textToSpeech({
        processId: processWithUniqueFileName.id, // Use the ID from the processWithUniqueFileName object
        text: processWithUniqueFileName.text,
        voice: JSON.parse(JSON.stringify(processWithUniqueFileName.voice)),
        outputPath: processWithUniqueFileName.outputPath,
        fileName: processWithUniqueFileName.fileName, // Sử dụng fileName duy nhất
        language: 'en'
        // ... additional parameters from processWithUniqueFileName if needed ...
      });

      if (ttsResult && 'error' in ttsResult) {
        // Handle error if textToSpeech fails to start
        console.error('Error initiating textToSpeech:', ttsResult.error);
        showToast('error', ttsResult.error);
        // Optional: Update the status of this specific process to 'failed' in the frontend list
        // Find the process by ID and update its status/error message
         const index = processes.value.findIndex(p => p.id === processWithUniqueFileName.id);
         if (index !== -1) {
           processes.value[index].status = 'failed';
           processes.value[index].error = ttsResult.error;
           processes.value[index].updatedAt = new Date().toISOString();
         }
      } else {
        // Process initiated successfully. Backend should send status updates.
        console.log('TextToSpeech initiated for process:', processWithUniqueFileName.id);
      }

      return newProcess;
    } catch (error) {
      console.error('Error creating or initiating process:', error);
      showToast('error', String(error));

      // Also update the process status to failed in case of a sync error before backend call completes
      const index = processes.value.findIndex(p => p.id === newProcess.id);
      if (index !== -1) {
        processes.value[index].status = 'failed';
        processes.value[index].error = String(error);
        processes.value[index].updatedAt = new Date().toISOString();
      }

      throw error; // Re-throw the error to be handled by the caller (HomeView)
    }
  };

  // Update process status
  const updateProcessStatus = (data: ProcessStatusUpdate) => {
    const processIndex = processes.value.findIndex((p) => p.id === data.processId);
    if (processIndex !== -1) {
      const currentProcess = processes.value[processIndex];

      // Nếu status chuyển sang processing hoặc running và chưa có startTime, thì gán startTime
      const startTime = (data.status === 'processing' || data.status === 'running') && !currentProcess.startTime
        ? new Date().toISOString()
        : currentProcess.startTime;

      // Cập nhật chunk info nếu có
      if (data.chunkInfo) {
        // Nếu chưa có chunks, reload processes để lấy chunks mới
        if (!currentProcess.chunks || currentProcess.chunks.length === 0) {
          console.log('No chunks found, reloading processes to get chunks data');
          loadProcesses();
          return;
        }

        const chunkIndex = currentProcess.chunks.findIndex(c => c.id === data.chunkInfo!.id);
        if (chunkIndex !== -1) {
          currentProcess.chunks[chunkIndex] = {
            ...currentProcess.chunks[chunkIndex],
            ...data.chunkInfo
          };

          // Tính lại tổng duration khi chunk hoàn thành
          if (data.chunkInfo.status === 'completed' && data.chunkInfo.duration) {
            const totalDuration = currentProcess.chunks.reduce((sum, chunk) => sum + (chunk.duration || 0), 0);
            console.log('Chunk hoàn thành, cập nhật duration', {
              chunkId: data.chunkInfo.id,
              chunkDuration: data.chunkInfo.duration,
              totalDuration
            });
          }
        }
      }

      // Xử lý thông báo tất cả chunk đã hoàn thành
      if (data.allChunksCompleted && currentProcess.chunks) {
        const totalDuration = currentProcess.chunks.reduce((sum, chunk) => sum + (chunk.duration || 0), 0);
        console.log('Tất cả chunk đã hoàn thành, chuẩn bị merge audio', {
          processId: data.processId,
          totalChunks: currentProcess.chunks.length,
          completedChunks: currentProcess.chunks.filter(c => c.status === 'completed').length,
          totalDuration: totalDuration
        });
      }

      processes.value[processIndex] = {
        ...currentProcess,
        status: data.status,
        progress: data.progress ?? currentProcess.progress,
        error: data.error,
        audioPath: data.audioPath,
        executionTime: data.executionTime ?? currentProcess.executionTime,
        duration: data.duration ?? currentProcess.duration,
        startTime,
        updatedAt: data.updatedAt || new Date().toISOString(),
        // Đảm bảo chunks được preserve
        chunks: currentProcess.chunks || [],
      };
    } else {
      console.warn('Process not found in current list, reloading processes...', data.processId);
      loadProcesses();
    }
  };

  // Cancel process
  const cancelProcess = async (processId: string) => {
    try {
      await window.electronAPI.cancelProcess(processId);
      showToast('success', t('home.process.actions.cancelSuccess'));
    } catch (error) {
      console.error('Error cancelling process:', error);
      showToast('error', String(error));
      throw error;
    }
  };

  // Delete process
  const deleteProcess = async (processId: string) => {
    try {
      await window.electronAPI.removeProcess(processId);
      processes.value = processes.value.filter(p => p.id !== processId);
      showToast('success', t('home.process.actions.deleteSuccess'));
    } catch (error) {
      console.error('Error deleting process:', error);
      showToast('error', String(error));
      throw error;
    }
  };

  // Cleanup processes - sử dụng API hàng loạt mới
  const cleanupProcesses = async () => {
    try {
      // Gọi API cleanup hàng loạt, trả về danh sách process còn lại
      processes.value = await window.electronAPI.cleanupCompletedProcesses();

      showToast('success', t('home.process.actions.cleanupSuccess'));
    } catch (error) {
      console.error('Error cleaning up processes:', error);
      showToast('error', t('home.process.actions.cleanupError'));
      throw error;
    }
  };

  // Toggle process details
  const toggleProcessDetails = (processId: string) => {
    if (expandedProcesses.value.has(processId)) {
      expandedProcesses.value.delete(processId);
    } else {
      expandedProcesses.value.add(processId);
    }
  };

  return {
    processes,
    expandedProcesses,
    sortedProcesses,
    loadProcesses,
    createProcess,
    updateProcessStatus,
    cancelProcess,
    deleteProcess,
    cleanupProcesses,
    toggleProcessDetails
  };
}