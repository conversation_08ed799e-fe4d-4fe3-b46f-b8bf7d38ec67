<script setup lang="ts">
import {ref} from 'vue';
import {useRouter} from 'vue-router';
import {Cog6ToothIcon, DocumentTextIcon, LanguageIcon, SpeakerWaveIcon} from '@heroicons/vue/24/outline';
import {useI18n} from 'vue-i18n';

const router = useRouter();
const {locale, t} = useI18n();

defineProps<{
  theme: 'light' | 'dark'
}>();

const emit = defineEmits<{
  (e: 'toggle-theme'): void
}>();

const navItems = [
  {name: 'Text To Speech', path: '/', icon: LanguageIcon},
  {name: 'Voices', path: '/voices', icon: SpeakerWaveIcon},
  {name: 'Settings', path: '/settings', icon: Cog6ToothIcon},
  {name: 'Logs', path: '/logs', icon: DocumentTextIcon},
];

const currentPath = ref(router.currentRoute.value.path);

// Cập nhật đường dẫn hiện tạ<PERSON> khi chuyển trang
router.afterEach((to) => {
  currentPath.value = to.path;
});

const languages = [
  {
    code: 'en',
    name: 'English',
    flag: '🇬🇧'
  },
  {
    code: 'vi',
    name: 'Tiếng Việt',
    flag: '🇻🇳'
  }
];

const toggleTheme = () => {
  emit('toggle-theme');
};
</script>

<template>
  <nav class="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-800 shadow-md">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <span class="text-xl font-bold text-gray-800 dark:text-white">{{ t('app.title') }}</span>
        </div>

        <!-- Navigation Items -->
        <div class="flex items-center space-x-4">
          <div v-for="item in navItems" :key="item.path"
               class="px-3 py-2 rounded-md text-sm font-medium cursor-pointer transition-colors duration-200"
               :class="[
                 currentPath === item.path
                   ? 'bg-blue-500 text-white'
                   : 'text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
               ]"
               @click="router.push(item.path)">
            <div class="flex items-center space-x-1">
              <component :is="item.icon" class="h-5 w-5"/>
              <span>{{ item.name }}</span>
            </div>
          </div>

          <!-- Language Selector -->
          <div class="relative">
            <select
                v-model="locale"
                class="pl-3 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white appearance-none"
            >
              <option v-for="lang in languages" :key="lang.code" :value="lang.code">
                {{ lang.flag }} {{ lang.name }}
              </option>
            </select>
            <div class="absolute inset-y-0 right-0 pr-2 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clip-rule="evenodd"/>
              </svg>
            </div>
          </div>

          <!-- Theme Toggle Button -->
          <button
              @click="toggleTheme"
              class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none"
          >
            <svg
                v-if="theme === 'light'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
            >
              <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
              />
            </svg>
            <svg
                v-else
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
            >
              <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Spacer to prevent content from being hidden behind the fixed navbar -->
  <div class="h-16"></div>
</template>

<style scoped>
select option {
  display: flex;
  align-items: center;
  padding: 8px;
}

select option::before {
  content: attr(data-flag);
  margin-right: 8px;
}
</style>
