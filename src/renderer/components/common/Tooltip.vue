<template>
  <div class="relative inline-block" ref="triggerRef" @mouseenter="showTooltip" @mouseleave="hideTooltip" @focusin="showTooltip" @focusout="hideTooltip">
    <slot name="trigger"></slot>
    <transition name="fade">
      <div v-if="show" ref="tooltipRef"
        class="absolute z-[9999] mt-2 px-3 py-2 rounded shadow-lg bg-gray-800 text-white text-xs min-w-[350px] max-w-screen-sm break-words"
        :style="tooltipStyle"
        :class="placementClass">
        <slot />
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';

const props = defineProps({
  placement: {
    type: String,
    default: 'top', // top, bottom, left, right
  }
});

const show = ref(false);
const tooltipStyle = ref<Record<string, string>>({});
const triggerRef = ref<HTMLElement | null>(null);
const tooltipRef = ref<HTMLElement | null>(null);

const showTooltip = async () => {
  show.value = true;
  await nextTick();
  adjustTooltipPosition();
};
const hideTooltip = () => {
  show.value = false;
};

function adjustTooltipPosition() {
  const trigger = triggerRef.value;
  const tooltip = tooltipRef.value;
  if (!trigger || !tooltip) return;

  // Reset style
  tooltipStyle.value = {};

  const triggerRect = trigger.getBoundingClientRect();
  const tooltipRect = tooltip.getBoundingClientRect();
  const viewportWidth = window.innerWidth;

  let left = trigger.offsetWidth / 2 - tooltipRect.width / 2;
  let minLeft = 0;
  let maxLeft = viewportWidth - tooltipRect.width;
  let triggerLeft = triggerRect.left + window.scrollX;
  let computedLeft = triggerLeft + left;

  // Nếu tooltip bị tràn trái
  if (computedLeft < minLeft) {
    tooltipStyle.value = { left: `${-triggerRect.left}px` };
  }
  // Nếu tooltip bị tràn phải
  else if (computedLeft > maxLeft) {
    tooltipStyle.value = { left: `${viewportWidth - triggerRect.left - tooltipRect.width}px` };
  } else {
    tooltipStyle.value = { left: `50%`, transform: `translateX(-50%)` };
  }
}

const placementClass = computed(() => {
  switch (props.placement) {
    case 'bottom':
      return 'top-full';
    case 'left':
      return 'right-full mr-2 top-1/2 -translate-y-1/2';
    case 'right':
      return 'left-full ml-2 top-1/2 -translate-y-1/2';
    default:
      return 'bottom-full mb-2';
  }
});
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.15s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style> 