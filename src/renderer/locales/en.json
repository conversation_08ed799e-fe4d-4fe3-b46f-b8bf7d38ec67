{"app": {"title": "Gen Voice", "theme": {"light": "Light", "dark": "Dark"}}, "common": {"loading": "Loading...", "error": "An error occurred", "success": "Success", "buttons": {"confirm": "Confirm", "cancel": "Cancel"}, "import": "Import", "importing": "Importing...", "cancel": "Cancel"}, "home": {"title": "Text To Speech", "form": {"text": {"label": "Text", "placeholder": "Enter text to convert...", "characterCount": {"empty": "No content", "count": "{count} characters", "minimum": "Minimum {minimum} characters"}}, "voice": {"label": "Select Voice", "placeholder": "Select voice"}, "language": {"label": "Language"}, "speed": {"label": "Speed: {speed}", "tooltipTitle": "Playback speed", "tooltip": "Adjust the playback speed of the voice. Value greater than 1 is faster, less than 1 is slower.", "exampleSlow": "Very slow", "exampleNormal": "Normal", "exampleFast": "Fast", "exampleVeryFast": "Very fast"}, "temperature": {"label": "Temperature: {value}", "tooltipTitle": "Sampling Temperature", "tooltip": "Adjusts the randomness in the audio generation process. Lower values (0.3-0.5) will produce more stable and consistent audio, while higher values will create more creative variations but may lose the original voice characteristics.", "exampleLow": "0.3-0.5: Stable, consistent audio", "exampleHigh": "0.7-1.0: More creative variations"}, "repetitionPenalty": {"label": "Repetition Penalty: {value}", "tooltipTitle": "Repetition Penalty", "tooltip": "Adjusts the penalty for repeating syllables or words. Higher values (3.0-5.0) will help avoid unwanted repetitions, but shouldn't be set too high to avoid losing natural voice rhythm.", "exampleLow": "1.0-2.0: Less repetition penalty", "exampleHigh": "3.0-5.0: Strong repetition penalty"}, "topK": {"label": "Top K: {value}", "tooltipTitle": "Number of Top Tokens", "tooltip": "Limits the number of potential choices when generating audio. Lower values (20-30) will help the model focus on high-probability samples and stay closer to the original voice.", "exampleLow": "20-30: Focus on high probability samples", "exampleHigh": "40-50: Consider more options"}, "topP": {"label": "Top P: {value}", "tooltipTitle": "Cumulative Probability Threshold", "tooltip": "Limits the cumulative probability when selecting tokens. Lower values (0.6-0.7) will help the model only consider high-probability choices, producing audio closer to the original voice.", "exampleLow": "0.6-0.7: Focus on high probability", "exampleHigh": "0.8-1.0: Consider more probabilities"}, "lengthPenalty": {"label": "Length Penalty: {value}", "tooltipTitle": "Length Adjustment Factor", "tooltip": "Adjusts the length of the generated audio. Lower values will produce shorter audio, while higher values will produce longer audio.", "exampleLow": "0.0-0.5: Shorter audio", "exampleHigh": "1.5-2.0: Longer audio"}, "numThreads": {"label": "Number of threads: {value}", "tooltipTitle": "Number of threads", "tooltip": "Number of CPU threads used for processing. Increasing threads can improve processing speed on multi-core machines. However, setting too high can overload the system.", "example2": "2 threads: Suitable for most computers (including MacBook Air)", "example4": "4 threads: Only for powerful machines (8+ cores)", "warning": "Note: Do not set higher than actual CPU cores"}, "numWorkers": {"label": "Number of workers: {value}", "tooltipTitle": "Number of workers", "tooltip": "Number of parallel workers processing TTS tasks. Each worker consumes significant RAM. Consider available RAM when adjusting this value.", "example2": "2 workers: Suitable for 8GB RAM (MacBook Air)", "example4": "4 workers: Only for machines with 16GB+ RAM", "warning": "Note: Each worker may use 2-4GB RAM, ensure sufficient available RAM for chosen workers"}, "outputFolder": {"label": "Output Folder", "placeholder": "Select output folder...", "select": "Select"}, "referenceFiles": {"label": "Reference Files", "placeholder": "Select reference audio files...", "select": "Select Files"}, "buttons": {"preview": "Preview", "stop": "Stop", "convert": "Convert", "processing": "Processing..."}, "fileName": {"label": "File Name", "placeholder": "Enter file name (optional)"}, "advancedOptions": {"label": "Advanced Options", "tooltip": "Show/hide advanced options"}}, "process": {"title": "Process", "empty": "No processes yet", "columns": {"text": "Text", "voice": "Voice", "status": "Status", "executionTime": "Execution Time", "duration": "Duration", "progress": "Progress", "fileName": "File Name", "actions": "Actions"}, "status": {"pending": "Pending", "processing": "Processing", "running": "Running", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled"}, "actions": {"play": "Play audio", "stop": "Stop", "delete": "Delete process", "cancel": "Cancel", "cancelSuccess": "Process cancelled", "deleteSuccess": "Process deleted", "cleanup": "Cleanup Processes", "cleanupSuccess": "Successfully cleaned up processes", "cleanupError": "Error cleaning up processes", "expand": "Expand", "collapse": "Collapse", "reload": "Reload chunk"}, "confirm": {"cancel": {"title": "Confirm Cancel", "message": "Are you sure you want to cancel this process?"}, "delete": {"title": "Confirm Delete", "message": "Are you sure you want to delete this process?"}, "cleanup": {"title": "Confirm Cleanup", "message": "Are you sure you want to clean up completed and failed processes?"}}, "chunks": {"title": "Chunks", "duration": "Duration: {duration}", "tokens": "Tokens: {tokens}", "noAudio": "No audio file available for this chunk", "playError": "Cannot play audio file", "reloadSuccess": "Chunk reloaded successfully"}}}, "voices": {"title": "Voice Management", "tabs": {"manage": "Manage Voices", "finetune": "Train Voice"}, "manage": {"empty": "No voices available", "actions": {"play": "Play", "edit": "Edit", "delete": "Delete", "export": "Export voice", "import": "Import Voice", "deleteSuccess": "Voice deleted successfully", "deleteError": "Failed to delete voice", "deleteConfirmTitle": "Delete Voice", "deleteConfirmMessage": "Are you sure you want to delete voice \"{name}\"? This action cannot be undone.", "noPreview": "No preview available for this voice", "playError": "Failed to play audio file"}, "export": {"success": "Voice exported successfully", "error": {"missingFiles": "Voice is missing required files", "failed": "Failed to export voice"}}, "import": {"title": "Import Voice", "nameLabel": "Voice Name", "namePlaceholder": "Enter voice name (optional)", "success": "Voice imported successfully", "error": {"failed": "Failed to import voice"}}}, "finetune": {"title": "Train New Voice", "form": {"voiceName": {"label": "Voice Name", "placeholder": "Enter name for new voice"}, "inputFolder": {"label": "Data Folder", "placeholder": "Path to folder containing wav and txt files", "select": "Select", "description": "Folder should contain pairs of audio files (.wav, .mp3, .flac) and .txt files with matching names (e.g. audio001.wav and audio001.txt)"}, "duration": {"good": "Good: 3-30 seconds", "acceptable": "Acceptable: <3s or 30-60 seconds", "notRecommended": "Not recommended: >60 seconds"}}, "metadata": {"title": "Process Training Resources", "processing": "Processing metadata...", "processed": "Metadata processed", "stats": {"totalFiles": "Total files:", "totalDuration": "Total duration:", "trainCount": "Training data:", "evalCount": "Evaluation data:"}, "table": {"file": "File", "duration": "Duration", "text": "Text"}, "buttons": {"process": "Process Training Resources", "startTraining": "Start Training", "cancel": "Cancel Processing"}}, "training": {"title": "Training Progress", "empty": "No training process running", "status": {"processing": "Processing", "completed": "Completed", "failed": "Failed"}, "progress": "Progress: {progress}%", "buttons": {"cancel": "Cancel Training"}}, "confirmCancel": {"title": "Confirm Cancel Training", "message": "Are you sure you want to cancel this training process? Completed progress will be lost and cannot be recovered.", "buttons": {"cancel": "Cancel", "confirm": "Confirm Cancel"}}}, "update": {"title": "Update Voice", "nameLabel": "Name", "namePlaceholder": "Enter voice name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter voice description", "languageLabel": "Language", "tagsLabel": "Tags", "tagsPlaceholder": "Enter tags (comma separated)", "tagsHint": "Separate tags with commas", "referenceLabel": "Reference Files", "reference": {"add": "Add Reference File", "maxLimit": "Maximum 5 reference files allowed", "error": {"add": "Failed to add reference file"}}, "speedLabel": "Speed", "temperatureLabel": "Temperature", "repetitionPenaltyLabel": "Repetition Penalty", "topKLabel": "Top K", "topPLabel": "Top P", "lengthPenaltyLabel": "Length Penalty", "modelPathLabel": "Model Path", "configPathLabel": "Config Path", "vocabPathLabel": "Vocab Path", "previewPathLabel": "Preview Path", "buttons": {"update": "Update", "select": "Select"}, "error": {"nameRequired": "Voice name is required", "selectFile": "Failed to select file", "failed": "Failed to update voice", "invalidFileType": "Invalid file type. Please select the correct file format"}, "success": "Voice updated successfully"}}, "cleanup": {"title": "Clean up processes", "message": "This will remove all completed, failed and cancelled processes. Are you sure?", "cleanupSuccess": "Processes cleaned up successfully", "cleanupError": "Could not clean up processes"}}