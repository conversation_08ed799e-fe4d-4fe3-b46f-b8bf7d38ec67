<script setup lang="ts">
import {onMounted, ref} from 'vue';
import type {AppSettings, Voice} from '@types';

// State cho settings
const settings = ref<AppSettings>({
  theme: 'light',
  language: 'vi',
  speed: 1.0,
  defaultVoice: '',
  outputPath: '',
  voicesPath: ''
});

// State cho danh sách voices
const voices = ref<Voice[]>([]);

// Load settings khi component được mount
onMounted(async () => {
  // Load settings từ electron-store
  settings.value.theme = await window.electronAPI.getStoreValue('theme') || 'light';
  settings.value.language = await window.electronAPI.getStoreValue('language') || 'en';
  settings.value.speed = await window.electronAPI.getStoreValue('speed') || 1.0;
  settings.value.defaultVoice = await window.electronAPI.getStoreValue('defaultVoice') || '';
  settings.value.outputPath = await window.electronAPI.getStoreValue('outputPath') || '';
  settings.value.voicesPath = await window.electronAPI.getStoreValue('voicesPath') || '';

  // TODO: Load danh sách voices
});

// Hàm chọn thư mục đầu ra
const selectOutputPath = async () => {
  const folderPath = await window.electronAPI.selectFolder();
  if (folderPath) {
    settings.value.outputPath = folderPath;
    await window.electronAPI.setStoreValue('outputPath', folderPath);
  }
};

// Hàm chọn thư mục voices
const selectVoicesPath = async () => {
  const folderPath = await window.electronAPI.selectFolder();
  if (folderPath) {
    settings.value.voicesPath = folderPath;
    await window.electronAPI.setStoreValue('voicesPath', folderPath);
  }
};

// Hàm lưu cài đặt
const saveSettings = async () => {
  await window.electronAPI.setStoreValue('theme', settings.value.theme);
  await window.electronAPI.setStoreValue('language', settings.value.language);
  await window.electronAPI.setStoreValue('speed', settings.value.speed);
  await window.electronAPI.setStoreValue('defaultVoice', settings.value.defaultVoice);

  // Hiển thị thông báo thành công
  alert('Đã lưu cài đặt thành công!');
};

// Hàm mở thư mục userData
const openUserDataFolder = async () => {
  try {
    await window.electronAPI.openUserDataFolder();
  } catch (error) {
    console.error('Lỗi khi mở thư mục userData:', error);
    alert('Không thể mở thư mục userData');
  }
};
</script>

<template>
  <div>
    <h1 class="text-2xl font-bold mb-6 text-gray-800 dark:text-white">Cài đặt</h1>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <form @submit.prevent="saveSettings">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

          <!-- Output Path -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Thư mục lưu file</label>
            <div class="flex">
              <input
                  v-model="settings.outputPath"
                  type="text"
                  class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Đường dẫn thư mục lưu file"
                  readonly
              />
              <button
                  type="button"
                  @click="selectOutputPath"
                  class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-r-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Chọn
              </button>
            </div>
          </div>

          <!-- Voices Path -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Thư mục lưu voice</label>
            <div class="flex">
              <input
                  v-model="settings.voicesPath"
                  type="text"
                  class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Đường dẫn thư mục lưu voice"
                  readonly
              />
              <button
                  type="button"
                  @click="selectVoicesPath"
                  class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-r-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Chọn
              </button>
            </div>
          </div>

          <!-- UserData Folder -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Thư mục dữ liệu ứng dụng</label>
            <button
                type="button"
                @click="openUserDataFolder"
                class="w-full px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Mở thư mục
            </button>
          </div>
        </div>

        <div class="mt-6 flex justify-end">
          <button
              type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Lưu cài đặt
          </button>
        </div>
      </form>
    </div>
  </div>
</template>
