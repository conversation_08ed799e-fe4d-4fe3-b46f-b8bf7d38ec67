import path from 'path';
import {WindowManager} from './WindowManager';
import {runPythonScript} from './PythonService';
import type {TextToSpeechParams} from '@types';
import fs from 'fs';
import logger from '../logger/client';
import { getStoreValue, createStore } from '@main/utils/store-helper';
import {TTSProcessStore} from '../store/TTSProcessStore';

interface PythonScriptResult {
  error?: string;
  output?: string;
}

export class SpeechService {
  private static instance: SpeechService;
  private windowManager = WindowManager.getInstance();
  private store = createStore();
  private log = logger.channel('tts');
  private processStore: TTSProcessStore;

  private constructor() {
    this.processStore = TTSProcessStore.getInstance();
  }

  public static getInstance(): SpeechService {
    if (!SpeechService.instance) {
      SpeechService.instance = new SpeechService();
    }
    return SpeechService.instance;
  }

  /**
   * Chuyển đổi văn bản thành giọng nói
   * @param params Tham số đầu vào cho quá trình chuyển đổi
   * @returns Thông tin về quá trình xử lý và đường dẫn file output
   */
  public async textToSpeech(params: TextToSpeechParams) {
    try {
      this.log.info("Bắt đầu quá trình chuyển đổi văn bản thành giọng nói", {...params});
      const processId = params.processId

      // 1. Chuẩn bị các file và thư mục
      const {chunksDir, outputPath} = this.prepareOutputFiles(params);

      // 2. Tách văn bản thành các đoạn nhỏ
      const chunks = await this.splitTextIntoChunks(params.text, 'en', processId);

      // Cập nhật trạng thái running sau khi tách text thành công
      const process = this.processStore.getProcess(processId);
      if (process) {
        process.status = 'running';
        process.progress = 0;
        process.updatedAt = new Date().toISOString();
        this.processStore.updateProcess(process);
      }

      this.windowManager.sendToRenderer('processStatusUpdate', {
        processId,
        status: 'running',
        progress: 0,
        updatedAt: new Date().toISOString(),

      });

      // 3. Xử lý từng đoạn văn bản
      const outputPaths = await this.processChunks(chunks, params, chunksDir, processId);

      // 4. Gộp các file âm thanh
      await this.mergeAudioFiles(outputPaths, outputPath);

      // 5. Dọn dẹp các file processor input/output
      const processorInputPath = path.join(chunksDir, 'processor_input.json');
      const processorOutputPath = path.join(chunksDir, 'processor_output.json');
      if (fs.existsSync(processorInputPath)) {
        fs.unlinkSync(processorInputPath);
      }
      if (fs.existsSync(processorOutputPath)) {
        fs.unlinkSync(processorOutputPath);
      }

      // Cập nhật trạng thái completed
      const latestProcess = this.processStore.getProcess(processId);
      if (latestProcess) {
        const executionTime = latestProcess.startTime ?
          new Date().getTime() - new Date(latestProcess.startTime).getTime() : undefined;

        latestProcess.status = 'completed';
        latestProcess.progress = 100;
        latestProcess.audioPath = outputPath;
        latestProcess.executionTime = executionTime;
        latestProcess.updatedAt = new Date().toISOString();
        this.processStore.updateProcess(latestProcess);
      }

      this.windowManager.sendToRenderer('processStatusUpdate', {
        processId,
        status: 'completed',
        progress: 100,
        audioPath: outputPath,
        executionTime: latestProcess?.executionTime,
        updatedAt: new Date().toISOString()
      });

      this.log.info("Đã hoàn thành quá trình chuyển đổi văn bản thành giọng nói", {processId, outputPath});
      return {processId, outputPath};
    } catch (error) {
      this.log.error('Error running text-to-speech:', error);
      return {error: String(error)};
    }
  }

  /**
   * Chuẩn bị các file và thư mục cần thiết cho quá trình xử lý
   * @param params Tham số đầu vào
   * @param processId ID của quá trình xử lý
   * @returns Thông tin về các đường dẫn file và thư mục
   */
  private prepareOutputFiles(params: TextToSpeechParams): { chunksDir: string; outputPath: string } {
    // Tạo thư mục gen_voice_chunks trong outputPath
    const totalChunksDir = path.join(params.outputPath, 'gen_voice_chunks');
    fs.mkdirSync(totalChunksDir, {recursive: true});

    // Tạo thư mục con cho process theo fileName
    const chunksDir = path.join(totalChunksDir, params.fileName);
    fs.mkdirSync(chunksDir, {recursive: true});

    // Đường dẫn file output audio (lưu trực tiếp trong outputPath)
    const outputPath = path.join(params.outputPath, `${params.fileName}.wav`);

    return {chunksDir, outputPath};
  }

  /**
   * Tách văn bản thành các đoạn nhỏ để xử lý
   * @param text Văn bản cần tách
   * @param language Ngôn ngữ của văn bản
   * @param processId ID của tiến trình
   * @returns Mảng các đoạn văn bản đã tách
   */
  public async splitTextIntoChunks(text: string, language: string, processId: string): Promise<string[]> {
    this.log.debug("Bắt đầu tách text", {text: text.slice(0, 50) + "...", language});
    const outputPathJson = path.join(getStoreValue(this.store, 'tempDir') as string, `tts_${processId}`, 'chunks.json');
    fs.mkdirSync(path.dirname(outputPathJson), {recursive: true});
    fs.writeFileSync(outputPathJson, '[]', 'utf-8');

    // Cập nhật trạng thái processing và gán startTime
    const process = this.processStore.getProcess(processId);
    if (process) {
      process.status = 'processing';
      process.startTime = new Date().toISOString(); // Ghi lại thời điểm bắt đầu
      process.updatedAt = new Date().toISOString();
      this.processStore.updateProcess(process);
    }

    // Gửi trạng thái processing
    this.windowManager.sendToRenderer('processStatusUpdate', {
      processId,
      status: 'processing',
      progress: 0,
      updatedAt: new Date().toISOString()
    });

    // Thay thế toàn bộ ký tự " trong text thành rỗng
    const safeText = text.replace(/"/g, '');

    const splitResult = await runPythonScript('text_utils', [
      '--text', JSON.stringify(safeText),
      '--output', outputPathJson,
      '--language', language,
      '--process_id', processId
    ]) as PythonScriptResult;

    if (splitResult.error) {
      this.log.error("Lỗi khi tách text", {error: splitResult.error});
      throw new Error(splitResult.error);
    }

    const jsonContent = fs.readFileSync(outputPathJson, 'utf-8');
    const chunks = JSON.parse(jsonContent);

    if (!chunks || chunks.length === 0) {
      this.log.error("Không thể tách text thành các đoạn");
      throw new Error('Không thể tách text thành các đoạn');
    }

    this.log.debug("Đã tách text thành công", {numChunks: chunks.length});
    return chunks;
  }

  /**
   * Xử lý các đoạn văn bản thành file âm thanh
   * @param chunks Các đoạn văn bản cần xử lý
   * @param params Tham số đầu vào
   * @param chunksDir Thư mục lưu trữ chunks
   * @param processId
   * @returns Mảng đường dẫn các file âm thanh đã tạo
   */
  private async processChunks(chunks: string[], params: TextToSpeechParams, chunksDir: string, processId: string): Promise<string[]> {
    this.log.debug("Bắt đầu xử lý các đoạn", {numChunks: chunks.length});
    const chunksOutputs = chunks.map((_: any, index: number) =>
        path.join(chunksDir, `chunk_${index + 1}.wav`)
    );

    // Tạo thông tin chunks cho process
    const process = this.processStore.getProcess(processId);
    if (process) {
      process.chunks = chunks.map((text, index) => ({
        id: `${processId}_chunk_${index + 1}`,
        text,
        status: 'pending',
        audioPath: chunksOutputs[index]
      }));
      this.processStore.updateProcess(process);
    }

    const argsList = chunks.map((chunk: string, index: number) => {
      return this.buildTTSArgs({
        ...params,
        text: chunk,
        outputPath: chunksOutputs[index],
        processId
      });
    });

    const processorInput = {
      chunks,
      num_workers: params.numWorkers || 1,
      args_list: argsList
    };

    const processorInputPath = path.join(chunksDir, 'processor_input.json');
    fs.writeFileSync(processorInputPath, JSON.stringify(processorInput));

    // Gửi trạng thái running với progress 0
    this.windowManager.sendToRenderer('processStatusUpdate', {
      processId,
      status: 'running',
      progress: 0,
      updatedAt: new Date().toISOString()
    });

    const processorResult = await runPythonScript('tts_processor', [
      processorInputPath,
      '--process_id', processId
    ], {
      onProgress: (data) => {
        // Cập nhật trạng thái với progress từ Python
        if (process && process.chunks && data.chunk_info) {
          // Tìm chunk theo ID từ chunk_info
          const chunkIndex = process.chunks.findIndex(c => c.id === data.chunk_info.id);
          if (chunkIndex !== -1) {
            const chunk = process.chunks[chunkIndex];
            chunk.status = data.chunk_info.status;
            if (data.chunk_info.audioPath) {
              chunk.audioPath = data.chunk_info.audioPath;
            }
            if (data.chunk_info.duration) {
              chunk.duration = data.chunk_info.duration;
            }
            if (data.chunk_info.error) {
              chunk.error = data.chunk_info.error;
            }
            this.processStore.updateProcess(process);

            // Gửi cập nhật chunk đến renderer
            this.windowManager.sendToRenderer('processStatusUpdate', {
              processId,
              status: 'running',
              progress: data.progress || 0,
              chunkInfo: {
                id: chunk.id,
                status: data.chunk_info.status,
                audioPath: data.chunk_info.audioPath,
                duration: data.chunk_info.duration
              },
              updatedAt: new Date().toISOString()
            });
          }
        }

        // Kiểm tra nếu tất cả chunk đã hoàn thành
        if (data.all_chunks_completed && process && process.chunks) {
          const allCompleted = process.chunks.every(chunk => chunk.status === 'completed');
          if (allCompleted) {
            this.log.info("Tất cả chunk đã hoàn thành, chuẩn bị merge audio", {
              processId,
              totalChunks: process.chunks.length
            });

            // Gửi thông báo tất cả chunk đã hoàn thành
            this.windowManager.sendToRenderer('processStatusUpdate', {
              processId,
              status: 'running',
              progress: 100,
              allChunksCompleted: true,
              updatedAt: new Date().toISOString()
            });
          }
        }
      },
      onLog: (log) => {
        try {
          const data = JSON.parse(log);
          if (data && data.status === 'completed' && data.output_path && process && process.chunks && data.chunk_index !== undefined) {
            const chunk = process.chunks[data.chunk_index];
            if (chunk) {
              chunk.status = 'completed';
              chunk.audioPath = data.output_path;
              chunk.duration = data.duration;
              this.processStore.updateProcess(process);

              // Cập nhật trạng thái cho renderer
              this.windowManager.sendToRenderer('processStatusUpdate', {
                processId,
                status: 'running',
                progress: data.progress || 100,
                chunkInfo: {
                  id: chunk.id,
                  status: 'completed',
                  audioPath: data.output_path,
                  duration: data.duration
                },
                updatedAt: new Date().toISOString()
              });
            }
            this.log.debug("Chunk completed", {
              processId,
              outputPath: data.output_path
            });
          }
        } catch {
        }
      }
    }, processId) as PythonScriptResult;

    if (processorResult.error) {
      // Cập nhật trạng thái lỗi cho tất cả chunks
      if (process && process.chunks) {
        process.chunks.forEach(chunk => {
          chunk.status = 'error';
          chunk.error = processorResult.error;
        });
        this.processStore.updateProcess(process);
      }
      throw new Error(processorResult.error);
    }

    // Đọc kết quả từ file tạm
    const outputFile = path.join(chunksDir, 'processor_output.json');
    if (!fs.existsSync(outputFile)) {
      throw new Error('Không tìm thấy file kết quả từ processor');
    }

    try {
      const outputContent = fs.readFileSync(outputFile, 'utf-8');
      const results = JSON.parse(outputContent) as { output_paths?: string[] };

      if (!results.output_paths) {
        throw new Error('Không nhận được kết quả từ processor');
      }

      this.log.debug("Đã xử lý các đoạn thành công", {numOutputs: results.output_paths.length});
      return results.output_paths;
    } catch (error) {
      this.log.error("Lỗi khi đọc kết quả từ processor", {error, outputFile});
      throw error;
    }
  }

  /**
   * Tạo danh sách tham số cho lệnh chuyển văn bản thành giọng nói
   * @param params Tham số đầu vào
   * @returns Mảng các tham số dòng lệnh
   */
  private buildTTSArgs(params: TextToSpeechParams): string[] {
    const args = [
      '--text', params.text,
      '--output', params.outputPath,
      '--model_path', params.voice.modelPath,
      '--language', params.language || 'en',
      '--speed', (params.voice.speed ?? 1.0).toString(),
      '--temperature', (params.voice.temperature ?? 0.7).toString(),
      '--repetition_penalty', (params.voice.repetitionPenalty ?? 2.0).toString(),
      '--top_k', (params.voice.topK ?? 50).toString(),
      '--top_p', (params.voice.topP ?? 0.85).toString(),
      '--length_penalty', (params.voice.lengthPenalty ?? 1.0).toString(),
      '--num_threads', (params.numThreads ?? 1).toString(),
      '--process_id', params.processId || ''
    ];

    // Thêm speaker_wav nếu có referencePath
    if (params.voice.referencePath && params.voice.referencePath.length > 0) {
      // Kiểm tra và chuẩn hóa đường dẫn file
      const validPaths = params.voice.referencePath.filter(p => {
        const exists = fs.existsSync(p);
        if (!exists) {
          this.log.warning(`File not found: ${p}`);
        }
        return exists;
      });

      if (validPaths.length === 0) {
        throw new Error('No valid reference audio files found');
      }

      // Thêm từng file một cách riêng biệt
      validPaths.forEach(p => {
        args.push('--speaker_wav', p);
      });
    } else {
      throw new Error('Voice reference path is required');
    }

    // Thêm các tham số tùy chọn
    if (params.voice.configPath) {
      args.push('--config_path', params.voice.configPath);
    }
    if (params.voice.vocabPath) {
      args.push('--vocab_path', params.voice.vocabPath);
    }

    this.log.debug("Đã tạo args cho TTS", {args});
    return args;
  }

  /**
   * Gộp các file âm thanh thành một file duy nhất
   * @param outputPaths Mảng đường dẫn các file âm thanh cần gộp
   * @param outputPath Đường dẫn file output
   */
  private async mergeAudioFiles(outputPaths: string[], outputPath: string): Promise<void> {
    this.log.debug("Bắt đầu gộp các file âm thanh", {numFiles: outputPaths.length, outputPath});
    const mergeResult = await runPythonScript(
        'audio_utils',
        [
          '--input', JSON.stringify(outputPaths),
          '--output', outputPath,
          '--sample_rate', '24000',
          '--gap_duration', '0.1'
        ]
    ) as PythonScriptResult;

    if (mergeResult.error) {
      this.log.error("Lỗi khi gộp file âm thanh", {error: mergeResult.error});
      throw new Error(mergeResult.error);
    }

    this.log.debug("Đã gộp các file âm thanh thành công");
  }

  /**
   * Reload một chunk và tự động gộp lại với các chunks khác
   * @param processId ID của process
   * @param chunkId ID của chunk cần reload
   * @param text Text của chunk
   * @param voice Voice settings
   * @returns Kết quả của quá trình reload
   */
  public async reloadChunk(params: { processId: string; chunkId: string; text: string; voice: any }) {
    try {
      const {processId, chunkId, text, voice} = params;
      this.log.info("Bắt đầu reload chunk", {processId, chunkId});

      // Lấy process từ store
      const process = this.processStore.getProcess(processId);
      if (!process) {
        throw new Error('Process not found');
      }

      // Tìm chunk cần reload
      const chunkIndex = process.chunks?.findIndex(c => c.id === chunkId);
      if (chunkIndex === -1 || !process.chunks) {
        throw new Error('Chunk not found');
      }

      // Tạo thư mục tạm cho chunk
      const tempDir = path.join(getStoreValue(this.store, 'tempDir') as string, `tts_${processId}`);
      const tempOutput = path.join(tempDir, `chunk_${chunkIndex}.wav`);

      // Cập nhật trạng thái chunk
      process.chunks[chunkIndex].status = 'running';
      this.processStore.updateProcess(process);

      // Xử lý chunk
      const args = this.buildTTSArgs({
        text,
        voice,
        outputPath: tempOutput,
        processId,
        language: 'en',
        fileName: process.fileName
      });

      const processorInput = {
        chunks: [text],
        num_workers: 1,
        args_list: [args]
      };

      const processorInputPath = path.join(tempDir, 'processor_input.json');
      fs.writeFileSync(processorInputPath, JSON.stringify(processorInput));

      // Gọi Python script để xử lý
      const processorResult = await runPythonScript('tts_processor', [
        processorInputPath,
        '--process_id', processId
      ], {
        onProgress: (data) => {
          if (process.chunks) {
            process.chunks[chunkIndex].status = 'completed';
            this.processStore.updateProcess(process);
          }
        },
        onLog: (log) => {
          try {
            const data = JSON.parse(log);
            if (data && data.status === 'completed' && data.output_path && process.chunks) {
              process.chunks[chunkIndex].status = 'completed';
              process.chunks[chunkIndex].audioPath = data.output_path;
              process.chunks[chunkIndex].duration = data.duration;
              this.processStore.updateProcess(process);
            }
          } catch {
          }
        }
      }, processId) as PythonScriptResult;

      if (processorResult.error) {
        throw new Error(processorResult.error);
      }

      // Đọc kết quả từ file tạm
      const outputFile = path.join(tempDir, 'processor_output.json');
      if (!fs.existsSync(outputFile)) {
        throw new Error('Không tìm thấy file kết quả từ processor');
      }

      const outputContent = fs.readFileSync(outputFile, 'utf-8');
      const results = JSON.parse(outputContent) as { output_paths?: string[] };

      if (!results.output_paths || results.output_paths.length === 0) {
        throw new Error('Không nhận được kết quả từ processor');
      }

      // Tự động gộp lại các chunks
      const allChunks = process.chunks.map((chunk, index) => {
        if (index === chunkIndex) {
          return results.output_paths[0];
        }
        return path.join(tempDir, `chunk_${index}.wav`);
      });

      // Gộp các file âm thanh
      const outputPath = process.audioPath || path.join(getStoreValue(this.store, 'outputPath') as string, `${processId}.wav`);
      await this.mergeAudioFiles(allChunks, outputPath);

      // Cập nhật trạng thái process
      process.status = 'completed';
      process.progress = 100;
      process.audioPath = outputPath;
      process.updatedAt = new Date().toISOString();
      this.processStore.updateProcess(process);

      this.log.info("Đã reload chunk thành công", {processId, chunkId});
      return {success: true};
    } catch (error) {
      this.log.error('Error reloading chunk:', error);
      return {error: String(error)};
    }
  }

}