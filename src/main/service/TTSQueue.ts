import type {TTSQueueItem} from '@types';
import logger from '../logger/client';
import {WindowManager} from './WindowManager';
import {TTSProcessStore} from '../store/TTSProcessStore';

export class TTSQueue {
  private static instance: TTSQueue;
  private queue: TTSQueueItem[] = [];
  private isProcessing = false;
  private readonly maxConcurrentTasks: number;
  private activeTasks: Map<string, TTSQueueItem> = new Map();
  private log = logger.channel('tts-queue');
  private windowManager: WindowManager;
  private processStore: TTSProcessStore;

  private constructor(maxConcurrentTasks: number = 1) {
    this.maxConcurrentTasks = maxConcurrentTasks;
    this.windowManager = WindowManager.getInstance();
    this.processStore = TTSProcessStore.getInstance();
  }

  public static getInstance(): TTSQueue {
    if (!TTSQueue.instance) {
      TTSQueue.instance = new TTSQueue();
    }
    return TTSQueue.instance;
  }

  public async addTask(task: TTSQueueItem): Promise<void> {
    // Đảm bảo task có processId
    if (!task.processId) {
      throw new Error('Task must have a processId');
    }

    // Kiểm tra xem processId đã tồn tại trong queue hoặc đang xử lý chưa
    if (this.queue.some(item => item.processId === task.processId) ||
        this.activeTasks.has(task.processId)) {
      throw new Error(`Task with processId ${task.processId} already exists`);
    }

    this.queue.push(task);
    this.log.info('TTS-QUEUE : Added new task to queue', {processId: task.processId});

    // Cập nhật trạng thái trong store
    const process = this.processStore.getProcess(task.processId);
    if (process) {
      process.status = 'pending';
      process.progress = 0;
      process.updatedAt = new Date().toISOString();
      this.processStore.updateProcess(process);
    }

    // Gửi trạng thái processing khi task được thêm vào queue
    this.windowManager.sendToRenderer('processStatusUpdate', {
      processId: task.processId,
      status: 'pending',
      progress: 0,
      updatedAt: new Date().toISOString()
    });

    // Bắt đầu xử lý queue nếu chưa có task nào đang chạy
    if (!this.isProcessing) {
      this.processNextTask();
    }
  }

  private async processNextTask(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) return;

    this.isProcessing = true;
    try {
      while (this.queue.length > 0 && this.activeTasks.size < this.maxConcurrentTasks) {
        const task = this.queue.shift();
        if (!task) continue;

        // Sử dụng processId từ task
        const processId = task.processId;
        this.activeTasks.set(processId, task);

        logger.info('TTS-QUEUE : Processing TTS task', {processId});

        try {
          await task.execute();
        } catch (error) {
          logger.error('TTS-QUEUE : Error processing task', {processId, error});
          // Cập nhật trạng thái lỗi trong store
          const process = this.processStore.getProcess(processId);
          if (process) {
            const executionTime = process.startTime ?
              new Date().getTime() - new Date(process.startTime).getTime() : undefined;

            process.status = 'error';
            process.error = String(error);
            process.executionTime = executionTime;
            process.updatedAt = new Date().toISOString();
            this.processStore.updateProcess(process);

            // Gửi thông báo lỗi về renderer
            this.windowManager.sendToRenderer('processStatusUpdate', {
              processId,
              status: 'error',
              error: String(error),
              executionTime,
              updatedAt: new Date().toISOString()
            });
          }
          // Gửi thông báo lỗi về renderer
          if (task.onError) {
            task.onError(error);
          }
        } finally {
          this.activeTasks.delete(processId);
        }
      }
    } finally {
      this.isProcessing = false;
      if (this.queue.length > 0) {
        this.processNextTask();
      }
    }
  }

  async cancelTask(processId: string): Promise<boolean> {
    // Kiểm tra trong queue
    const queueIndex = this.queue.findIndex(item => item.processId === processId);
    if (queueIndex !== -1) {
      this.queue.splice(queueIndex, 1);
      logger.info('TTS-QUEUE : Cancelled queued task', {processId});

      // Cập nhật trạng thái trong store
      const process = this.processStore.getProcess(processId);
      if (process) {
        const executionTime = process.startTime ?
          new Date().getTime() - new Date(process.startTime).getTime() : undefined;

        process.status = 'cancelled';
        process.executionTime = executionTime;
        process.updatedAt = new Date().toISOString();
        this.processStore.updateProcess(process);

        // Gửi thông báo về renderer
        this.windowManager.sendToRenderer('processStatusUpdate', {
          processId,
          status: 'cancelled',
          executionTime,
          updatedAt: new Date().toISOString()
        });
      }
      return true;
    }

    // Kiểm tra trong active tasks
    const activeTask = this.activeTasks.get(processId);
    if (activeTask) {
      // Gọi hàm cancel của task nếu có
      if (activeTask.cancel) {
        await activeTask.cancel();
      }
      this.activeTasks.delete(processId);
      logger.info('TTS-QUEUE : Cancelled active task', {processId});

      // Cập nhật trạng thái trong store
      const process = this.processStore.getProcess(processId);
      if (process) {
        const executionTime = process.startTime ?
          new Date().getTime() - new Date(process.startTime).getTime() : undefined;

        process.status = 'cancelled';
        process.executionTime = executionTime;
        process.updatedAt = new Date().toISOString();
        this.processStore.updateProcess(process);

        // Gửi thông báo về renderer
        this.windowManager.sendToRenderer('processStatusUpdate', {
          processId,
          status: 'cancelled',
          executionTime,
          updatedAt: new Date().toISOString()
        });
      }
      return true;
    }

    return false;
  }

  async deleteTask(processId: string): Promise<boolean> {
    // Hủy task nếu đang chạy
    const cancelled = await this.cancelTask(processId);

    // Xóa khỏi store (async)
    await this.processStore.deleteProcess(processId);

    return cancelled;
  }

  getQueueStatus(): { queued: number; active: number } {
    return {
      queued: this.queue.length,
      active: this.activeTasks.size
    };
  }
}