import type {default as Store} from 'electron-store';
import type {TTSProcess, TTSProcessStoreInterface} from '@types';
import {logger} from '@main/logger/client';
import {createStore, getStoreValue, setStoreValue} from '@main/utils/store-helper';
import * as fs from 'fs';
import * as path from 'path';

export class TTSProcessStore {
  private static instance: TTSProcessStore;
  private store: Store<TTSProcessStoreInterface>;

  private constructor() {
    this.store = createStore<TTSProcessStoreInterface>({
      name: 'tts_processes',
      defaults: {
        processes: [],
      },
    });
  }

  public static getInstance(): TTSProcessStore {
    if (!TTSProcessStore.instance) {
      TTSProcessStore.instance = new TTSProcessStore();
    }
    return TTSProcessStore.instance;
  }

  // Lấy tất cả processes
  getProcesses(): TTSProcess[] {
    return getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
  }

  // Lấy process theo id
  getProcess(id: string): TTSProcess | null {
    const processes = getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
    return processes.find((process) => process.id === id) || null;
  }

  // Thêm process mới
  createProcess(process: TTSProcess): void {
    const processes = getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
    processes.push(process);
    setStoreValue(this.store, 'processes', processes);
  }

  // Cập nhật process
  updateProcess(updatedProcess: TTSProcess): void {
    try {
      logger.info(`Bắt đầu cập nhật process với id: ${updatedProcess.id}`);
      const processes = getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
      const index = processes.findIndex((p) => p.id === updatedProcess.id);

      if (index === -1) {
        logger.error(`Không tìm thấy process với id ${updatedProcess.id}`);
        throw new Error('Process not found');
      }

      logger.info('Thông tin process cũ:', {
        ...processes[index],
      });

      logger.info('Thông tin process mới:', {
        ...updatedProcess,
      });

      // Cập nhật process trong store
      processes[index] = updatedProcess;
      setStoreValue(this.store, 'processes', processes);

      logger.info(`Đã cập nhật process thành công: ${updatedProcess.id}`);
    } catch (error) {
      logger.error('Lỗi khi cập nhật process:', error);
      throw error;
    }
  }

  /**
   * Xóa các file chunk audio và thư mục liên quan
   * @param process Process cần xóa file
   */
  private async cleanupProcessFiles(process: TTSProcess): Promise<void> {
    try {
      // Xóa các chunk audio files
      if (process.chunks && process.chunks.length > 0) {
        for (const chunk of process.chunks) {
          if (chunk.audioPath && fs.existsSync(chunk.audioPath)) {
            try {
              fs.unlinkSync(chunk.audioPath);
              logger.debug(`Đã xóa chunk audio file: ${chunk.audioPath}`);
            } catch (error) {
              logger.warning(`Không thể xóa chunk audio file: ${chunk.audioPath}`, error);
            }
          }
        }

        // Xóa thư mục chunks nếu có
        if (process.chunks[0]?.audioPath) {
          const chunksDir = path.dirname(process.chunks[0].audioPath);
          if (fs.existsSync(chunksDir)) {
            try {
              // Kiểm tra xem thư mục có rỗng không trước khi xóa
              const files = fs.readdirSync(chunksDir);
              if (files.length === 0) {
                fs.rmdirSync(chunksDir);
                logger.debug(`Đã xóa thư mục chunks: ${chunksDir}`);
              }
            } catch (error) {
              logger.warning(`Không thể xóa thư mục chunks: ${chunksDir}`, error);
            }
          }
        }
      }

      // Xóa file audio chính nếu có
      // if (process.audioPath && fs.existsSync(process.audioPath)) {
      //   try {
      //     fs.unlinkSync(process.audioPath);
      //     logger.debug(`Đã xóa file audio chính: ${process.audioPath}`);
      //   } catch (error) {
      //     logger.warning(`Không thể xóa file audio chính: ${process.audioPath}`, error);
      //   }
      // }
    } catch (error) {
      logger.error('Lỗi khi dọn dẹp file process:', error);
    }
  }

  // Xóa process
  async deleteProcess(id: string): Promise<void> {
    try {
      const processes = getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
      const process = processes.find((p) => p.id === id);

      if (!process) {
        logger.error(`Không tìm thấy process với id ${id}`);
        return;
      }

      // Xóa các file liên quan
      await this.cleanupProcessFiles(process);

      // Xóa process khỏi danh sách
      const filteredProcesses = processes.filter((p) => p.id !== id);
      setStoreValue(this.store, 'processes', filteredProcesses);

      logger.info(`Đã xóa process thành công: ${id}`);
    } catch (error) {
      logger.error('Lỗi khi xóa process:', error);
      throw error;
    }
  }

  /**
   * Xóa hàng loạt các process đã hoàn thành/thất bại/bị hủy
   * @returns Danh sách process còn lại sau khi cleanup
   */
  async cleanupCompletedProcesses(): Promise<TTSProcess[]> {
    try {
      const processes = getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');

      // Tìm các process cần xóa
      const processesToCleanup = processes.filter(p =>
        p.status === 'completed' || p.status === 'failed' || p.status === 'cancelled'
      );

      // Tìm các process còn lại
      const remainingProcesses = processes.filter(p =>
        p.status !== 'completed' && p.status !== 'failed' && p.status !== 'cancelled'
      );

      logger.info(`Bắt đầu cleanup ${processesToCleanup.length} processes`);

      // Xóa file của từng process
      for (const process of processesToCleanup) {
        try {
          await this.cleanupProcessFiles(process);
          logger.debug(`Đã cleanup files cho process: ${process.id}`);
        } catch (error) {
          logger.warning(`Lỗi khi cleanup files cho process ${process.id}:`, error);
        }
      }

      // Cập nhật store với danh sách process còn lại
      setStoreValue(this.store, 'processes', remainingProcesses);

      logger.info(`Đã cleanup thành công ${processesToCleanup.length} processes, còn lại ${remainingProcesses.length} processes`);

      return remainingProcesses;
    } catch (error) {
      logger.error('Lỗi khi cleanup processes:', error);
      throw error;
    }
  }
}