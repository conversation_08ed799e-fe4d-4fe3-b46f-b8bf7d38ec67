import type {default as Store} from 'electron-store';
import type {TTSProcess, TTSProcessStoreInterface} from '@types';
import {logger} from '@main/logger/client';
import {createStore, getStoreValue, setStoreValue} from '@main/utils/store-helper';

export class TTSProcessStore {
  private static instance: TTSProcessStore;
  private store: Store<TTSProcessStoreInterface>;

  private constructor() {
    this.store = createStore<TTSProcessStoreInterface>({
      name: 'tts_processes',
      defaults: {
        processes: [],
      },
    });
  }

  public static getInstance(): TTSProcessStore {
    if (!TTSProcessStore.instance) {
      TTSProcessStore.instance = new TTSProcessStore();
    }
    return TTSProcessStore.instance;
  }

  // Lấy tất cả processes
  getProcesses(): TTSProcess[] {
    return getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
  }

  // Lấy process theo id
  getProcess(id: string): TTSProcess | null {
    const processes = getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
    return processes.find((process) => process.id === id) || null;
  }

  // Thêm process mới
  createProcess(process: TTSProcess): void {
    const processes = getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
    processes.push(process);
    setStoreValue(this.store, 'processes', processes);
  }

  // Cập nhật process
  updateProcess(updatedProcess: TTSProcess): void {
    try {
      logger.info(`Bắt đầu cập nhật process với id: ${updatedProcess.id}`);
      const processes = getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
      const index = processes.findIndex((p) => p.id === updatedProcess.id);

      if (index === -1) {
        logger.error(`Không tìm thấy process với id ${updatedProcess.id}`);
        throw new Error('Process not found');
      }

      logger.info('Thông tin process cũ:', {
        ...processes[index],
      });

      logger.info('Thông tin process mới:', {
        ...updatedProcess,
      });

      // Cập nhật process trong store
      processes[index] = updatedProcess;
      setStoreValue(this.store, 'processes', processes);

      logger.info(`Đã cập nhật process thành công: ${updatedProcess.id}`);
    } catch (error) {
      logger.error('Lỗi khi cập nhật process:', error);
      throw error;
    }
  }

  // Xóa process
  deleteProcess(id: string): void {
    try {
      const processes = getStoreValue<TTSProcessStoreInterface, 'processes'>(this.store, 'processes');
      const process = processes.find((p) => p.id === id);

      if (!process) {
        logger.error(`Không tìm thấy process với id ${id}`);
        return;
      }

      // Xóa process khỏi danh sách
      const filteredProcesses = processes.filter((p) => p.id !== id);
      setStoreValue(this.store, 'processes', filteredProcesses);

      logger.info(`Đã xóa process thành công: ${id}`);
    } catch (error) {
      logger.error('Lỗi khi xóa process:', error);
      throw error;
    }
  }
}