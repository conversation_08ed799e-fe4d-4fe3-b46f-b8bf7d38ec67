import json
import os
import sys
from concurrent.futures import ProcessPoolExecutor
from typing import Dict

from logger import get_logger
from text_utils import split_text_by_tokens
from audio_utils import get_audio_duration

# Khởi tạo logger
log = get_logger("tts_processor", "tts", min_level="debug")


def process_chunk(args: Dict) -> str | None:
  try:
    # Log input args
    log.data("Bắt đầu xử lý chunk", {
      "process_id": args.get("process_id"),
      "chunk_index": args.get("chunk_index"),
      "status": "running",
      "progress": 0,
      "chunk_info": {
        "id": f"{args.get('process_id')}_chunk_{args.get('chunk_index')}",
        "text": args.get('text', ''),
        "status": "running",
        "progress": 0,
        "audioPath": args.get('output'),
        "duration": 0.0
      }
    })

    # Tách text thành các đoạn nhỏ hơn nếu cần
    text = args.get('text', '')
    language = args.get('language', 'en')
    max_tokens = args.get('max_tokens', 240)

    # Tách text
    text_chunks = split_text_by_tokens(text, language, max_tokens)
    log.info(f"Đã tách text thành {len(text_chunks)} chunks")

    # Xử lý từng chunk
    results = []
    for i, chunk in enumerate(text_chunks):
      # Cập nhật args với chunk mới
      chunk_args = args.copy()
      chunk_args['text'] = chunk
      chunk_args['chunk_index'] = f"{args.get('chunk_index', 0)}_{i}"

      # Chuyển args dict thành command line args
      cmd_args = []
      for key, value in chunk_args.items():
        if key == 'speaker_wav':
          # Xử lý đặc biệt cho speaker_wav - luôn truyền dưới dạng list
          if isinstance(value, list):
            cmd_args.extend([f'--{key}'] + value)
          else:
            # Nếu là string, tách thành list
            cmd_args.extend(
                [f'--{key}'] + [wav.strip() for wav in value.split(',') if
                                wav.strip()])
        else:
          cmd_args.extend([f'--{key}', str(value)])

      # Import và chạy tts.py
      from tts import main as tts_main
      # Lưu sys.argv gốc
      original_argv = sys.argv
      try:
        # Set sys.argv mới
        sys.argv = ['tts.py'] + cmd_args
        # Chạy tts.py
        result = tts_main()
        if result == 0:
          results.append(chunk_args['output'])
        else:
          raise Exception("TTS process failed")
      finally:
        # Khôi phục sys.argv gốc
        sys.argv = original_argv

    # Nếu có nhiều kết quả, gộp chúng lại
    if len(results) > 1:
      from audio_utils import merge_audio_files
      output_path = args['output']
      merge_audio_files(results, output_path)
      return output_path
    elif len(results) == 1:
      return results[0] or ''
    else:
      raise Exception("No results from TTS process")

  except Exception as e:
    log.data("Lỗi khi xử lý chunk", {
      "process_id": args.get("process_id"),
      "chunk_index": args.get("chunk_index"),
      "status": "error",
      "error": str(e),
      "chunk_info": {
        "id": f"{args.get('process_id')}_chunk_{args.get('chunk_index')}",
        "text": args.get('text', ''),
        "status": "error",
        "progress": 0,
        "audioPath": args.get('output'),
        "duration": 0.0,
        "error": str(e)
      }
    })
    # Re-raise exception để future.result() có thể catch được
    raise Exception(f"Chunk processing failed: {str(e)}")


def main():
  try:
    if len(sys.argv) < 2:
      log.error("Missing input file")
      return 1

    input_file = sys.argv[1]
    log.info("Reading input file", {"file": input_file})

    # Lấy process_id từ args nếu có
    process_id = None
    for i in range(2, len(sys.argv)):
      if sys.argv[i] == '--process_id' and i + 1 < len(sys.argv):
        process_id = sys.argv[i + 1]
        break

    if process_id:
      log.set_process_id(process_id)

    with open(input_file, 'r') as f:
      data = json.load(f)

    chunks = data.get('chunks', [])
    num_workers = data.get('num_workers', 1)
    args_list = data.get('args_list', [])

    # Log input data
    log.info("Input data", {
      "num_chunks": len(chunks),
      "num_workers": num_workers,
      "num_args": len(args_list),
      "process_id": process_id
    })

    if not chunks or not args_list:
      log.error("Invalid input data", {"process_id": process_id})
      return 1

    results = []
    completed_chunks = 0
    total_chunks = len(chunks)

    with ProcessPoolExecutor(max_workers=num_workers) as executor:
      # Map chunks với args tương ứng
      chunk_args = []
      for chunk_index, (chunk, args) in enumerate(zip(chunks, args_list)):
        # Convert args từ list sang dict nếu cần
        if isinstance(args, list):
          args_dict = {}
          for i in range(0, len(args), 2):
            if i + 1 < len(args):
              key = args[i].lstrip('-')
              args_dict[key] = args[i + 1]
          args = args_dict

        # Merge args với chunk
        chunk_args.append({
          **args,
          'text': chunk,
          'process_id': process_id,
          'chunk_index': chunk_index
        })

      # Log chunk args
      log.info("Chunk args",
               {"num_chunks": len(chunk_args), "process_id": process_id})

      # Chạy các chunks song song
      futures = [executor.submit(process_chunk, args) for args in chunk_args]

      # Lấy kết quả theo thứ tự
      chunk_results = []
      for i, future in enumerate(futures):
        try:
          result = future.result()
          if result is None:
            log.error("Chunk result is None", {
              "chunk_index": i,
              "process_id": process_id
            })
            return 1

          chunk_results.append((i, result))
          completed_chunks += 1

          # Lấy thông tin chunk tương ứng
          chunk_arg = chunk_args[i]

          # Tính duration thực tế của file âm thanh
          duration = get_audio_duration(result) if result else 0.0

          chunk_info = {
            "id": f"{process_id}_chunk_{i + 1}",
            "text": chunk_arg.get('text', ''),
            "status": "completed",
            "audioPath": result,
            "duration": duration
          }

          # Tính progress
          progress = int((completed_chunks / total_chunks) * 100)

          log.data("Cập nhật tiến độ", {
            "process_id": process_id,
            "status": "running",
            "progress": progress,
            "completed_chunks": completed_chunks,
            "total_chunks": total_chunks,
            "chunk_info": chunk_info
          })

          # Log result
          log.info("Chunk result",
                   {"output_path": result, "process_id": process_id})

        except Exception as e:
          log.error("Error getting future result",
                    {"error": str(e), "process_id": process_id})
          return 1

      # Sắp xếp kết quả theo thứ tự chunk
      chunk_results.sort(key=lambda x: x[0])
      results = [result for _, result in chunk_results if result is not None]

      # Đảm bảo tất cả chunk đã được xử lý
      if len(results) != total_chunks:
        log.error("Không đủ kết quả chunk hợp lệ", {
          "expected": total_chunks,
          "actual_valid_results": len(results),
          "total_chunk_results": len(chunk_results),
          "process_id": process_id
        })
        return 1

      # Gửi thông báo hoàn thành tất cả chunk
      log.data("Tất cả chunk đã hoàn thành", {
        "process_id": process_id,
        "status": "running",
        "progress": 100,
        "completed_chunks": total_chunks,
        "total_chunks": total_chunks,
        "all_chunks_completed": True
      })

    # Ghi kết quả vào file tạm
    output_file = os.path.join(os.path.dirname(input_file), 'processor_output.json')
    with open(output_file, 'w') as f:
      json.dump({"output_paths": results}, f)

    log.info("Processing completed",
             {"output_file": output_file, "process_id": process_id})
    return 0

  except Exception as e:
    log.error("Error in main", {"error": str(e), "process_id": process_id})
    return 1


if __name__ == "__main__":
  main()
